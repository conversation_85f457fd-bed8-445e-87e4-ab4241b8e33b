import { computed, inject, ref, watch } from 'vue';
import { isNotEmpty } from '@primeuix/utils';

/**
 * 用于自定义组件与PrimeVue Form集成的composable
 * 实现了BaseEditableHolder的核心功能
 */
export function useFormField(props, emit) {
    // 注入表单上下文
    const $pcForm = inject('$pcForm', undefined);
    const $pcFormField = inject('$pcFormField', undefined);

    // 响应式数据
    const d_value = ref(props.defaultValue !== undefined ? props.defaultValue : props.modelValue);
    const formField = ref({});

    // 计算属性
    const $formName = computed(() => {
        return !$formNovalidate.value ? props.name || $formControl.value?.name : undefined;
    });

    const $formControl = computed(() => {
        return props.formControl || $pcFormField?.formControl;
    });

    const $formNovalidate = computed(() => {
        return $formControl.value?.novalidate;
    });

    const $invalid = computed(() => {
        return !$formNovalidate.value && findNonEmpty(
            props.invalid, 
            $pcFormField?.field?.invalid, 
            $pcForm?.getFieldState($formName.value)?.invalid
        );
    });

    const errorMessage = computed(() => formField.value?.error?.message);

    // 工具函数
    const findNonEmpty = (...values) => {
        return values.find(isNotEmpty);
    };

    // 核心方法：更新表单值
    const writeValue = (value, event) => {
        d_value.value = value;
        emit('update:modelValue', value);
        emit('value-change', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 监听器
    watch(() => props.modelValue, (newValue) => {
        d_value.value = newValue;
    });

    watch(() => props.defaultValue, (newValue) => {
        d_value.value = newValue;
    });

    // 注册到表单
    watch($formName, (newValue) => {
        formField.value = $pcForm?.register?.(newValue, $formControl.value) || {};
    }, { immediate: true });

    watch($formControl, (newValue) => {
        formField.value = $pcForm?.register?.($formName.value, newValue) || {};
    }, { immediate: true });

    return {
        d_value,
        formField,
        $invalid,
        errorMessage,
        writeValue
    };
}
