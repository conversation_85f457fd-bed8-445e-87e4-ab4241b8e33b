import { computed, inject, ref, watch } from 'vue';

/**
 * 用于自定义组件与PrimeVue Form集成的composable
 */
export function useFormField(props, emit) {
    // 注入表单上下文
    const $pcForm = inject('$pcForm', undefined);
    const $pcFormField = inject('$pcFormField', undefined);

    // 响应式数据
    const d_value = ref(props.defaultValue ?? props.modelValue);
    const formField = ref({});

    // 计算属性
    const $invalid = computed(() => {
        return props.invalid || $pcFormField?.field?.invalid || formField.value?.invalid;
    });

    const errorMessage = computed(() => formField.value?.error?.message);

    // 核心方法：更新表单值
    const writeValue = (value, event) => {
        d_value.value = value;
        emit('update:modelValue', value);
        emit('value-change', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 监听器
    watch(() => props.modelValue, (newValue) => {
        d_value.value = newValue;
    });

    // 注册到表单
    watch(() => props.name, (newValue) => {
        if (newValue && $pcForm?.register) {
            formField.value = $pcForm.register(newValue, props.formControl) || {};
        }
    }, { immediate: true });

    return {
        d_value,
        formField,
        $invalid,
        errorMessage,
        writeValue
    };
}
