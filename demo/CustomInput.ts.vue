<template>
    <input
        type="text"
        :value="d_value"
        :name="name"
        @input="onInput"
        @blur="onBlur"
    />
</template>

<script setup lang="ts">
import { useFormField } from './useFormField';

interface Props {
    name?: string;
    modelValue?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

// 使用表单字段composable
const { d_value, formField, writeValue } = useFormField(props, emit);

// 事件处理
const onInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    writeValue(target.value, event);
};

const onBlur = (event: Event) => {
    formField.value.onBlur?.(event);
};
</script>
