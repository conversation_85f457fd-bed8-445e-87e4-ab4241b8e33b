import { inject, ref, watch } from 'vue';

export function useFormField(props: any, emit: any) {
    // 注入表单上下文
    const $pcForm = inject('$pcForm', undefined);
    
    // 响应式数据
    const d_value = ref(props.modelValue);
    const formField = ref({});

    // 核心方法：更新表单值
    const writeValue = (value: any, event?: any) => {
        d_value.value = value;
        emit('update:modelValue', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 监听modelValue变化
    watch(() => props.modelValue, (newValue) => {
        d_value.value = newValue;
    });

    // 注册到表单
    watch(() => props.name, (newValue) => {
        if (newValue && $pcForm?.register) {
            formField.value = $pcForm.register(newValue) || {};
        }
    }, { immediate: true });

    return {
        d_value,
        formField,
        writeValue
    };
}
