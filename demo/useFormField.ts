import { inject, ref, watch } from 'vue';

interface FormField {
    onChange?: (data: { originalEvent?: any; value: any }) => void;
    onBlur?: (event?: any) => void;
}

interface PrimeForm {
    register?: (name: string) => FormField;
}

interface Props {
    name?: string;
    modelValue?: any;
}

interface Emit {
    (event: 'update:modelValue', value: any): void;
}

export function useFormField(props: Props, emit: Emit) {
    // 注入表单上下文
    const $pcForm = inject<PrimeForm | undefined>('$pcForm', undefined);

    // 响应式数据
    const d_value = ref(props.modelValue);
    const formField = ref<FormField>({});

    // 核心方法：更新表单值
    const writeValue = (value: any, event?: any) => {
        d_value.value = value;
        emit('update:modelValue', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 监听modelValue变化
    watch(() => props.modelValue, (newValue) => {
        d_value.value = newValue;
    });

    // 注册到表单
    watch(() => props.name, (newValue) => {
        if (newValue && $pcForm?.register) {
            formField.value = $pcForm.register(newValue) || {};
        }
    }, { immediate: true });

    return {
        d_value,
        formField,
        writeValue
    };
}
