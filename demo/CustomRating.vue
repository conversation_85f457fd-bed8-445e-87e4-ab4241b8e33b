<template>
    <div class="custom-rating-wrapper">
        <label v-if="label" class="custom-rating-label">{{ label }}</label>
        <div 
            class="custom-rating-container" 
            :class="{ 'invalid': $invalid, 'disabled': disabled }"
            @blur="onBlur"
            @focus="onFocus"
            tabindex="0"
        >
            <button
                v-for="star in stars"
                :key="star"
                type="button"
                class="custom-rating-star"
                :class="{ 'active': star <= d_value, 'hover': star <= hoverValue }"
                :disabled="disabled"
                @click="selectRating(star)"
                @mouseenter="hoverValue = star"
                @mouseleave="hoverValue = 0"
                :aria-label="`Rate ${star} out of ${stars}`"
            >
                <i class="pi pi-star-fill"></i>
            </button>
        </div>
        <div v-if="showValue" class="custom-rating-value">
            {{ d_value || 0 }} / {{ stars }}
        </div>
        <small v-if="$invalid && errorMessage" class="custom-rating-error">{{ errorMessage }}</small>
    </div>
</template>

<script>
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomRating',
    extends: BaseEditableHolder,
    inheritAttrs: false,
    emits: ['update:modelValue', 'value-change', 'focus', 'blur', 'change'],
    props: {
        label: {
            type: String,
            default: undefined
        },
        stars: {
            type: Number,
            default: 5
        },
        showValue: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            hoverValue: 0
        };
    },
    computed: {
        formFieldAttrs() {
            return this.formField || {};
        },
        errorMessage() {
            return this.formField?.error?.message || this.$pcFormField?.field?.error?.message;
        }
    },
    methods: {
        selectRating(rating) {
            if (!this.disabled) {
                // 调用父类的writeValue方法更新表单状态
                this.writeValue(rating, { target: { value: rating } });
                this.$emit('change', { target: { value: rating } });
            }
        },
        onBlur(event) {
            this.$emit('blur', event);
            // 触发表单字段的onBlur事件
            this.formField.onBlur?.(event);
        },
        onFocus(event) {
            this.$emit('focus', event);
        }
    }
};
</script>

<style scoped>
.custom-rating-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.custom-rating-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.custom-rating-container {
    display: flex;
    gap: 0.25rem;
    align-items: center;
    outline: none;
}

.custom-rating-container:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.25rem;
}

.custom-rating-star {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    font-size: 1.5rem;
    color: #d1d5db;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
}

.custom-rating-star:hover:not(:disabled) {
    transform: scale(1.1);
}

.custom-rating-star.active,
.custom-rating-star.hover {
    color: #fbbf24;
}

.custom-rating-star:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.custom-rating-container.disabled .custom-rating-star {
    cursor: not-allowed;
    opacity: 0.5;
}

.custom-rating-container.invalid {
    outline-color: #ef4444;
}

.custom-rating-value {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.custom-rating-error {
    color: #ef4444;
    font-size: 0.75rem;
}
</style>
