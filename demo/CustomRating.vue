<template>
    <div>
        <label v-if="label">{{ label }}</label>
        <div @blur="onBlur" tabindex="0">
            <button
                v-for="star in stars"
                :key="star"
                type="button"
                :disabled="disabled"
                @click="selectRating(star)"
            >
                {{ star <= d_value ? '★' : '☆' }}
            </button>
        </div>
        <div v-if="showValue">{{ d_value || 0 }} / {{ stars }}</div>
        <small v-if="$invalid && errorMessage">{{ errorMessage }}</small>
    </div>
</template>

<script>
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomRating',
    extends: BaseEditableHolder,
    inheritAttrs: false,
    emits: ['update:modelValue', 'value-change', 'focus', 'blur', 'change'],
    props: {
        label: String,
        stars: { type: Number, default: 5 },
        showValue: { type: Boolean, default: true }
    },
    computed: {
        formFieldAttrs() {
            return this.formField || {};
        },
        errorMessage() {
            return this.formField?.error?.message || this.$pcFormField?.field?.error?.message;
        }
    },
    methods: {
        selectRating(rating) {
            if (!this.disabled) {
                // 调用父类的writeValue方法更新表单状态
                this.writeValue(rating, { target: { value: rating } });
            }
        },
        onBlur(event) {
            // 触发表单字段的onBlur事件
            this.formField.onBlur?.(event);
        }
    }
};
</script>


