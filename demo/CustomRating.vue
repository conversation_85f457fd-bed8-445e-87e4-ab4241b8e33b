<template>
    <div>
        <label v-if="label">{{ label }}</label>
        <div @blur="onBlur" tabindex="0">
            <button
                v-for="star in stars"
                :key="star"
                type="button"
                :disabled="disabled"
                @click="selectRating(star)"
            >
                {{ star <= d_value ? '★' : '☆' }}
            </button>
        </div>
        <div v-if="showValue">{{ d_value || 0 }} / {{ stars }}</div>
        <small v-if="$invalid && errorMessage">{{ errorMessage }}</small>
    </div>
</template>

<script setup>
import { useFormField } from './useFormField.js';

// 定义props
const props = defineProps({
    label: String,
    stars: { type: Number, default: 5 },
    showValue: { type: Boolean, default: true },
    name: String,
    disabled: Boolean,
    modelValue: null,
    defaultValue: null,
    invalid: Boolean,
    formControl: Object
});

// 定义emits
const emit = defineEmits(['update:modelValue', 'value-change']);

// 使用表单字段composable
const { d_value, formField, $invalid, errorMessage, writeValue } = useFormField(props, emit);

// 事件处理方法
const selectRating = (rating) => {
    if (!props.disabled) {
        writeValue(rating, { target: { value: rating } });
    }
};

const onBlur = (event) => {
    formField.value.onBlur?.(event);
};
</script>


