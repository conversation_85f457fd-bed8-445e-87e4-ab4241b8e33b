<template>
    <div class="custom-tag-select-wrapper">
        <label v-if="label" class="custom-tag-label">{{ label }}</label>
        <div 
            class="custom-tag-container" 
            :class="{ 'invalid': $invalid, 'disabled': disabled, 'focused': focused }"
            @click="focusInput"
        >
            <!-- 已选择的标签 -->
            <div class="selected-tags">
                <span 
                    v-for="tag in selectedTags" 
                    :key="tag"
                    class="tag-item"
                >
                    {{ tag }}
                    <button 
                        v-if="!disabled"
                        type="button"
                        class="tag-remove"
                        @click.stop="removeTag(tag)"
                        :aria-label="`移除标签 ${tag}`"
                    >
                        <i class="pi pi-times"></i>
                    </button>
                </span>
            </div>
            
            <!-- 输入框 -->
            <input
                ref="inputRef"
                v-model="inputValue"
                type="text"
                class="tag-input"
                :placeholder="selectedTags.length === 0 ? placeholder : ''"
                :disabled="disabled"
                @keydown="onKeyDown"
                @focus="onFocus"
                @blur="onBlur"
                @input="onInput"
            />
        </div>
        
        <!-- 建议列表 -->
        <div v-if="showSuggestions && filteredOptions.length > 0" class="suggestions-list">
            <button
                v-for="(option, index) in filteredOptions"
                :key="option"
                type="button"
                class="suggestion-item"
                :class="{ 'highlighted': index === highlightedIndex }"
                @click="selectOption(option)"
                @mouseenter="highlightedIndex = index"
            >
                {{ option }}
            </button>
        </div>
        
        <small v-if="$invalid && errorMessage" class="custom-tag-error">{{ errorMessage }}</small>
    </div>
</template>

<script>
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomTagSelect',
    extends: BaseEditableHolder,
    inheritAttrs: false,
    emits: ['update:modelValue', 'value-change', 'focus', 'blur', 'change'],
    props: {
        label: {
            type: String,
            default: undefined
        },
        placeholder: {
            type: String,
            default: '输入标签...'
        },
        options: {
            type: Array,
            default: () => []
        },
        allowCustom: {
            type: Boolean,
            default: true
        },
        maxTags: {
            type: Number,
            default: undefined
        }
    },
    data() {
        return {
            inputValue: '',
            focused: false,
            showSuggestions: false,
            highlightedIndex: -1
        };
    },
    computed: {
        selectedTags() {
            return Array.isArray(this.d_value) ? this.d_value : [];
        },
        filteredOptions() {
            if (!this.inputValue) return [];
            return this.options.filter(option => 
                option.toLowerCase().includes(this.inputValue.toLowerCase()) &&
                !this.selectedTags.includes(option)
            );
        },
        formFieldAttrs() {
            return this.formField || {};
        },
        errorMessage() {
            return this.formField?.error?.message || this.$pcFormField?.field?.error?.message;
        }
    },
    methods: {
        focusInput() {
            if (!this.disabled) {
                this.$refs.inputRef?.focus();
            }
        },
        onFocus(event) {
            this.focused = true;
            this.showSuggestions = true;
            this.$emit('focus', event);
        },
        onBlur(event) {
            // 延迟隐藏建议列表，以便点击建议项
            setTimeout(() => {
                this.focused = false;
                this.showSuggestions = false;
                this.highlightedIndex = -1;
            }, 200);
            
            this.$emit('blur', event);
            this.formField.onBlur?.(event);
        },
        onInput() {
            this.showSuggestions = this.inputValue.length > 0;
            this.highlightedIndex = -1;
        },
        onKeyDown(event) {
            switch (event.key) {
                case 'Enter':
                    event.preventDefault();
                    if (this.highlightedIndex >= 0) {
                        this.selectOption(this.filteredOptions[this.highlightedIndex]);
                    } else if (this.inputValue.trim() && this.allowCustom) {
                        this.addTag(this.inputValue.trim());
                    }
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    this.highlightedIndex = Math.min(
                        this.highlightedIndex + 1, 
                        this.filteredOptions.length - 1
                    );
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.highlightedIndex = Math.max(this.highlightedIndex - 1, -1);
                    break;
                case 'Backspace':
                    if (!this.inputValue && this.selectedTags.length > 0) {
                        this.removeTag(this.selectedTags[this.selectedTags.length - 1]);
                    }
                    break;
                case 'Escape':
                    this.showSuggestions = false;
                    this.highlightedIndex = -1;
                    break;
            }
        },
        selectOption(option) {
            this.addTag(option);
        },
        addTag(tag) {
            if (!tag || this.selectedTags.includes(tag)) return;
            if (this.maxTags && this.selectedTags.length >= this.maxTags) return;
            
            const newTags = [...this.selectedTags, tag];
            this.updateValue(newTags);
            this.inputValue = '';
            this.showSuggestions = false;
            this.highlightedIndex = -1;
        },
        removeTag(tag) {
            const newTags = this.selectedTags.filter(t => t !== tag);
            this.updateValue(newTags);
        },
        updateValue(newTags) {
            this.writeValue(newTags, { target: { value: newTags } });
            this.$emit('change', { target: { value: newTags } });
        }
    },
    mounted() {
        // 确保初始值是数组
        if (!Array.isArray(this.d_value)) {
            this.writeValue([], { target: { value: [] } });
        }
    }
};
</script>

<style scoped>
.custom-tag-select-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.custom-tag-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.custom-tag-container {
    min-height: 2.5rem;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    cursor: text;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
}

.custom-tag-container:hover:not(.disabled) {
    border-color: #9ca3af;
}

.custom-tag-container.focused {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-tag-container.invalid {
    border-color: #ef4444;
}

.custom-tag-container.invalid.focused {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.custom-tag-container.disabled {
    background-color: #f9fafb;
    cursor: not-allowed;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.tag-remove:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.tag-input {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    min-width: 120px;
    font-size: 1rem;
    padding: 0.25rem 0;
}

.tag-input:disabled {
    cursor: not-allowed;
}

.suggestions-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
}

.suggestion-item {
    width: 100%;
    padding: 0.75rem 1rem;
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 1rem;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background-color: #f3f4f6;
}

.custom-tag-error {
    color: #ef4444;
    font-size: 0.75rem;
}
</style>
