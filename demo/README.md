# PrimeVue Form 自定义组件集成 Demo

展示如何创建自定义组件并与PrimeVue Form自动集成。

## 核心原理

1. **继承BaseEditableHolder**: 自定义组件继承`@primevue/core/baseeditableholder`
2. **调用writeValue**: 通过`writeValue()`方法触发表单状态更新
3. **处理表单事件**: 实现`onBlur`等事件处理

## 关键代码

### 自定义组件 (Vue3 Setup语法)
```vue
<script setup>
import { useFormField } from './useFormField.js';

const props = defineProps({
    name: String,
    disabled: Boolean,
    // ... 其他props
});

const emit = defineEmits(['update:modelValue', 'value-change', 'blur']);

// 使用表单字段composable
const { d_value, formField, $invalid, errorMessage, writeValue } = useFormField(props, emit);

// 事件处理
const onInput = (event) => {
    writeValue(event.target.value, event);
};

const onBlur = (event) => {
    formField.value.onBlur?.(event);
};
</script>
```

### useFormField Composable
```javascript
// useFormField.js - 可复用的表单字段逻辑
export function useFormField(props, emit) {
    const $pcForm = inject('$pcForm', undefined);
    const d_value = ref(props.defaultValue ?? props.modelValue);
    const formField = ref({});

    const writeValue = (value, event) => {
        d_value.value = value;
        emit('update:modelValue', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 注册到表单...

    return { d_value, formField, writeValue, /* ... */ };
}
```

### 使用方式

**方式一：直接使用**
```vue
<Form v-slot="$form" :resolver="resolver" @submit="onSubmit">
    <CustomInput name="email" label="邮箱" />
    <Message v-if="$form.email?.invalid" severity="error">
        {{ $form.email.error.message }}
    </Message>
</Form>
```

**方式二：FormField包装**
```vue
<Form :resolver="resolver" @submit="onSubmit">
    <FormField v-slot="$field" name="email">
        <CustomInput v-bind="$field.props" />
        <Message v-if="$field?.invalid" severity="error">
            {{ $field.error?.message }}
        </Message>
    </FormField>
</Form>
```

## 自动功能

✅ 自动值获取 - 无需v-model
✅ 验证状态显示
✅ 表单提交收集
✅ 状态管理 (touched/dirty)
✅ 事件处理
