# PrimeVue Form 自定义组件集成 Demo

这个demo展示了如何创建自定义组件并让它们与PrimeVue的Form组件自动集成，实现自动获取表单值的功能。

## 核心原理

PrimeVue的表单系统通过以下机制实现自动获取值：

1. **继承BaseEditableHolder**: 自定义组件继承`@primevue/core/baseeditableholder`
2. **表单注册机制**: 组件通过`$pcForm.register()`自动注册到表单
3. **事件委托**: 通过`writeValue()`方法触发表单状态更新
4. **依赖注入**: 通过`$pcForm`、`$pcFormField`等注入获取表单上下文

## 文件说明

### CustomInput.vue
一个自定义的输入组件，展示了基本的表单集成：
- 继承`BaseEditableHolder`
- 实现`onInput`、`onBlur`、`onChange`事件处理
- 支持验证状态显示
- 支持图标和标签

### CustomRating.vue  
一个自定义的评分组件，展示了复杂组件的表单集成：
- 星级评分界面
- 鼠标悬停效果
- 键盘导航支持
- 完整的表单状态管理

### FormDemo.vue
完整的demo页面，展示两种集成方式：

#### 方式一：直接使用
```vue
<Form v-slot="$form" :resolver="resolver" @submit="onFormSubmit">
    <CustomInput
        name="email"
        label="邮箱"
        placeholder="请输入邮箱地址"
    />
    <Message v-if="$form.email?.invalid" severity="error">
        {{ $form.email.error.message }}
    </Message>
</Form>
```

#### 方式二：通过FormField包装
```vue
<Form v-slot="$form" :resolver="resolver" @submit="onFormSubmit">
    <FormField v-slot="$field" name="customField" initialValue="">
        <CustomInput
            label="自定义字段"
            v-bind="$field.props"
        />
        <Message v-if="$field?.invalid" severity="error">
            {{ $field.error?.message }}
        </Message>
    </FormField>
</Form>
```

## 关键实现要点

### 1. 继承BaseEditableHolder
```javascript
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomInput',
    extends: BaseEditableHolder,
    // ...
}
```

### 2. 实现writeValue方法调用
```javascript
methods: {
    onInput(event) {
        // 这会自动触发表单状态更新
        this.writeValue(event.target.value, event);
    }
}
```

### 3. 处理表单字段事件
```javascript
onBlur(event) {
    this.$emit('blur', event);
    // 触发表单验证
    this.formField.onBlur?.(event);
}
```

### 4. 获取表单状态
```javascript
computed: {
    formFieldAttrs() {
        return this.formField || {};
    },
    errorMessage() {
        return this.formField?.error?.message;
    }
}
```

## 使用步骤

1. **安装依赖**:
   ```bash
   npm install @primevue/forms zod @primevue/forms/resolvers/zod
   ```

2. **创建自定义组件**: 继承`BaseEditableHolder`并实现必要的事件处理

3. **在表单中使用**: 直接使用或通过`FormField`包装

4. **配置验证**: 使用zod或其他验证库定义验证规则

## 自动集成的功能

✅ **自动值获取**: 无需手动绑定v-model  
✅ **验证状态**: 自动显示验证错误  
✅ **表单提交**: 自动收集所有字段值  
✅ **状态管理**: 自动跟踪touched、dirty等状态  
✅ **事件处理**: 自动处理blur、change等验证触发事件  

## 运行Demo

1. 将这些文件放入你的Vue项目中
2. 确保已安装PrimeVue和相关依赖
3. 导入并使用`FormDemo.vue`组件
4. 打开浏览器查看效果

这个demo完整展示了如何创建与PrimeVue Form系统完全集成的自定义组件！
