# PrimeVue Form 自定义组件集成 Demo (TypeScript)

展示如何用TypeScript创建能被PrimeVue Form自动识别的自定义组件。

## 核心原理

1. **注入表单上下文**: 通过`inject('$pcForm')`获取表单实例
2. **注册到表单**: 通过`$pcForm.register(name)`注册字段
3. **更新表单值**: 通过`writeValue()`触发表单状态更新

## 关键代码

### useFormField.ts - 核心Composable
```typescript
import { inject, ref, watch } from 'vue';

export function useFormField(props: any, emit: any) {
    const $pcForm = inject('$pcForm', undefined);
    const d_value = ref(props.modelValue);
    const formField = ref({});

    const writeValue = (value: any, event?: any) => {
        d_value.value = value;
        emit('update:modelValue', value);
        formField.value.onChange?.({ originalEvent: event, value });
    };

    // 注册到表单
    watch(() => props.name, (newValue) => {
        if (newValue && $pcForm?.register) {
            formField.value = $pcForm.register(newValue) || {};
        }
    }, { immediate: true });

    return { d_value, formField, writeValue };
}
```

### 自定义输入组件
```vue
<template>
    <input :value="d_value" :name="name" @input="onInput" @blur="onBlur" />
</template>

<script setup lang="ts">
import { useFormField } from './useFormField';

interface Props {
    name?: string;
    modelValue?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{ 'update:modelValue': [value: any] }>();

const { d_value, formField, writeValue } = useFormField(props, emit);

const onInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    writeValue(target.value, event);
};

const onBlur = (event: Event) => {
    formField.value.onBlur?.(event);
};
</script>
```

### 使用方式
```vue
<Form v-slot="$form" :resolver="resolver" @submit="onSubmit">
    <CustomInput name="email" />
    <Message v-if="$form.email?.invalid" severity="error">
        {{ $form.email.error.message }}
    </Message>

    <CustomRating name="rating" />
    <Message v-if="$form.rating?.invalid" severity="error">
        {{ $form.rating.error.message }}
    </Message>
</Form>
```

## 实现的功能

✅ **自动注册到表单** - 通过name属性自动识别
✅ **自动值收集** - 表单提交时自动获取值
✅ **验证集成** - 支持zod等验证库
✅ **TypeScript支持** - 完整的类型定义
