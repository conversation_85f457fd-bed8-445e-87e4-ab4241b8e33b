# PrimeVue Form 自定义组件集成 Demo

展示如何创建自定义组件并与PrimeVue Form自动集成。

## 核心原理

1. **继承BaseEditableHolder**: 自定义组件继承`@primevue/core/baseeditableholder`
2. **调用writeValue**: 通过`writeValue()`方法触发表单状态更新
3. **处理表单事件**: 实现`onBlur`等事件处理

## 关键代码

### 自定义组件
```javascript
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    extends: BaseEditableHolder,
    methods: {
        onInput(event) {
            // 自动更新表单状态
            this.writeValue(event.target.value, event);
        },
        onBlur(event) {
            // 触发表单验证
            this.formField.onBlur?.(event);
        }
    },
    computed: {
        formFieldAttrs() {
            return this.formField || {};
        },
        errorMessage() {
            return this.formField?.error?.message;
        }
    }
}
```

### 使用方式

**方式一：直接使用**
```vue
<Form v-slot="$form" :resolver="resolver" @submit="onSubmit">
    <CustomInput name="email" label="邮箱" />
    <Message v-if="$form.email?.invalid" severity="error">
        {{ $form.email.error.message }}
    </Message>
</Form>
```

**方式二：FormField包装**
```vue
<Form :resolver="resolver" @submit="onSubmit">
    <FormField v-slot="$field" name="email">
        <CustomInput v-bind="$field.props" />
        <Message v-if="$field?.invalid" severity="error">
            {{ $field.error?.message }}
        </Message>
    </FormField>
</Form>
```

## 自动功能

✅ 自动值获取 - 无需v-model
✅ 验证状态显示
✅ 表单提交收集
✅ 状态管理 (touched/dirty)
✅ 事件处理
