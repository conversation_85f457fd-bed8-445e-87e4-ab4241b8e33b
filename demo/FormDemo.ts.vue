<template>
    <div>
        <h1>PrimeVue Form 自定义组件集成 (TypeScript)</h1>
        
        <Form v-slot="$form" :resolver="resolver" @submit="onFormSubmit">
            <!-- PrimeVue原生组件 -->
            <InputText name="username" placeholder="用户名" />
            <Message v-if="$form.username?.invalid" severity="error">
                {{ $form.username.error.message }}
            </Message>

            <!-- 自定义组件 -->
            <CustomInput name="email" />
            <Message v-if="$form.email?.invalid" severity="error">
                {{ $form.email.error.message }}
            </Message>

            <CustomRating name="rating" />
            <Message v-if="$form.rating?.invalid" severity="error">
                {{ $form.rating.error.message }}
            </Message>

            <Button type="submit" label="提交" />
        </Form>
    </div>
</template>

<script setup lang="ts">
import { Form } from '@primevue/forms';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Message from 'primevue/message';
import { z } from 'zod';
import CustomInput from './CustomInput.ts.vue';
import CustomRating from './CustomRating.ts.vue';

// 验证规则
const schema = z.object({
    username: z.string().min(3, '用户名至少需要3个字符'),
    email: z.string().email('请输入有效的邮箱地址'),
    rating: z.number().min(1, '请至少给1星评分')
});

const resolver = zodResolver(schema);

const onFormSubmit = (formData: any) => {
    console.log('表单提交:', formData);
    alert('表单提交成功！');
};
</script>
