<template>
    <div>
        <h1>PrimeVue Form 自定义组件集成 Demo</h1>

        <h2>方式一：直接使用自定义组件</h2>
        <Form v-slot="$form" :resolver="resolver" @submit="onFormSubmit">
            <!-- PrimeVue原生组件 -->
            <div>
                <InputText name="username" placeholder="用户名" />
                <Message v-if="$form.username?.invalid" severity="error">
                    {{ $form.username.error.message }}
                </Message>
            </div>

            <!-- 自定义组件 -->
            <div>
                <CustomInput name="email" label="邮箱" placeholder="请输入邮箱" />
                <Message v-if="$form.email?.invalid" severity="error">
                    {{ $form.email.error.message }}
                </Message>
            </div>

            <div>
                <CustomRating name="rating" label="评分" />
                <Message v-if="$form.rating?.invalid" severity="error">
                    {{ $form.rating.error.message }}
                </Message>
            </div>

            <Button type="submit" label="提交" />
        </Form>

        <h2>方式二：使用FormField包装</h2>
        <Form :resolver="resolver2" @submit="onFormSubmit2">
            <!-- 包装原生元素 -->
            <FormField v-slot="$field" name="description">
                <textarea placeholder="描述" v-bind="$field.props"></textarea>
                <Message v-if="$field?.invalid" severity="error">
                    {{ $field.error?.message }}
                </Message>
            </FormField>

            <!-- 包装自定义组件 -->
            <FormField v-slot="$field" name="customField">
                <CustomInput label="自定义字段" v-bind="$field.props" />
                <Message v-if="$field?.invalid" severity="error">
                    {{ $field.error?.message }}
                </Message>
            </FormField>

            <Button type="submit" label="提交" />
        </Form>
    </div>
</template>

<script>
import { Form, FormField } from '@primevue/forms';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Message from 'primevue/message';
import { z } from 'zod';
import CustomInput from './CustomInput.vue';
import CustomRating from './CustomRating.vue';

export default {
    name: 'FormDemo',
    components: {
        Form,
        FormField,
        InputText,
        Button,
        Message,
        CustomInput,
        CustomRating
    },
    setup() {
        const schema = z.object({
            username: z.string().min(3, '用户名至少需要3个字符'),
            email: z.string().email('请输入有效的邮箱地址'),
            rating: z.number().min(1, '请至少给1星评分')
        });

        const schema2 = z.object({
            description: z.string().min(5, '描述至少需要5个字符'),
            customField: z.string().min(3, '字段至少需要3个字符')
        });

        const onFormSubmit = (formData) => {
            console.log('表单1提交:', formData);
            alert('表单1提交成功！');
        };

        const onFormSubmit2 = (formData) => {
            console.log('表单2提交:', formData);
            alert('表单2提交成功！');
        };

        return {
            resolver: zodResolver(schema),
            resolver2: zodResolver(schema2),
            onFormSubmit,
            onFormSubmit2
        };
    }
};
</script>


