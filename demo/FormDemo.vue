<template>
    <div class="form-demo">
        <h1>PrimeVue Form 自定义组件集成 Demo</h1>
        
        <div class="demo-section">
            <h2>方式一：直接在Form中使用自定义组件</h2>
            <Form 
                v-slot="$form" 
                :resolver="resolver" 
                :initialValues="initialValues"
                @submit="onFormSubmit" 
                class="demo-form"
            >
                <!-- 使用PrimeVue原生组件 -->
                <div class="form-field">
                    <label for="username">用户名 (PrimeVue InputText)</label>
                    <InputText 
                        id="username"
                        name="username" 
                        placeholder="请输入用户名" 
                        fluid 
                    />
                    <Message 
                        v-if="$form.username?.invalid" 
                        severity="error" 
                        size="small"
                    >
                        {{ $form.username.error.message }}
                    </Message>
                </div>

                <!-- 使用自定义输入组件 -->
                <div class="form-field">
                    <CustomInput
                        name="email"
                        label="邮箱 (自定义输入组件)"
                        placeholder="请输入邮箱地址"
                        icon="pi pi-envelope"
                        inputId="email"
                    />
                    <Message 
                        v-if="$form.email?.invalid" 
                        severity="error" 
                        size="small"
                    >
                        {{ $form.email.error.message }}
                    </Message>
                </div>

                <!-- 使用自定义评分组件 -->
                <div class="form-field">
                    <CustomRating
                        name="rating"
                        label="评分 (自定义评分组件)"
                        :stars="5"
                        :showValue="true"
                    />
                    <Message 
                        v-if="$form.rating?.invalid" 
                        severity="error" 
                        size="small"
                    >
                        {{ $form.rating.error.message }}
                    </Message>
                </div>

                <Button type="submit" label="提交表单" severity="secondary" />
            </Form>
        </div>

        <div class="demo-section">
            <h2>方式二：使用FormField包装自定义组件</h2>
            <Form 
                v-slot="$form" 
                :resolver="resolver2" 
                @submit="onFormSubmit2" 
                class="demo-form"
            >
                <!-- 使用FormField包装原生HTML元素 -->
                <FormField v-slot="$field" name="description" initialValue="" class="form-field">
                    <label for="description">描述 (原生textarea)</label>
                    <textarea
                        id="description"
                        placeholder="请输入描述"
                        rows="4"
                        :class="['native-textarea', { 'error': $field?.invalid }]"
                        v-bind="$field.props"
                    ></textarea>
                    <Message 
                        v-if="$field?.invalid" 
                        severity="error" 
                        size="small"
                    >
                        {{ $field.error?.message }}
                    </Message>
                </FormField>

                <!-- 使用FormField包装自定义组件 -->
                <FormField v-slot="$field" name="customField" initialValue="" class="form-field">
                    <CustomInput
                        label="自定义字段 (通过FormField)"
                        placeholder="通过FormField包装的自定义组件"
                        icon="pi pi-user"
                        v-bind="$field.props"
                    />
                    <Message 
                        v-if="$field?.invalid" 
                        severity="error" 
                        size="small"
                    >
                        {{ $field.error?.message }}
                    </Message>
                </FormField>

                <Button type="submit" label="提交表单" severity="secondary" />
            </Form>
        </div>

        <div class="demo-section">
            <h2>表单状态展示</h2>
            <div class="form-states">
                <h3>表单1状态：</h3>
                <pre>{{ JSON.stringify(formStates, null, 2) }}</pre>
            </div>
        </div>
    </div>
</template>

<script>
import { Form, FormField } from '@primevue/forms';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Message from 'primevue/message';
import { z } from 'zod';
import { ref } from 'vue';
import CustomInput from './CustomInput.vue';
import CustomRating from './CustomRating.vue';
import CustomTagSelect from './CustomTagSelect.vue';

export default {
    name: 'FormDemo',
    components: {
        Form,
        FormField,
        InputText,
        Button,
        Message,
        CustomInput,
        CustomRating
    },
    setup() {
        const formStates = ref({});

        // 第一个表单的验证规则
        const schema = z.object({
            username: z.string().min(3, '用户名至少需要3个字符'),
            email: z.string().email('请输入有效的邮箱地址'),
            rating: z.number().min(1, '请至少给1星评分').max(5, '评分不能超过5星')
        });

        // 第二个表单的验证规则
        const schema2 = z.object({
            description: z.string().min(10, '描述至少需要10个字符'),
            customField: z.string().min(5, '自定义字段至少需要5个字符')
        });

        const resolver = zodResolver(schema);
        const resolver2 = zodResolver(schema2);

        const initialValues = {
            username: '',
            email: '',
            rating: 0
        };

        const onFormSubmit = (formData) => {
            console.log('表单1提交:', formData);
            formStates.value = formData.states;
            alert('表单1提交成功！请查看控制台输出。');
        };

        const onFormSubmit2 = (formData) => {
            console.log('表单2提交:', formData);
            alert('表单2提交成功！请查看控制台输出。');
        };

        return {
            resolver,
            resolver2,
            initialValues,
            onFormSubmit,
            onFormSubmit2,
            formStates
        };
    }
};
</script>

<style scoped>
.form-demo {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.demo-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: #f9fafb;
}

.demo-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 500px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-field label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.native-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
    resize: vertical;
    font-family: inherit;
}

.native-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.native-textarea.error {
    border-color: #ef4444;
}

.form-states {
    background-color: white;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
}

.form-states pre {
    font-size: 0.75rem;
    color: #374151;
    white-space: pre-wrap;
    word-break: break-word;
}

h1 {
    color: #1f2937;
    margin-bottom: 2rem;
    text-align: center;
}

h2 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

h3 {
    color: #4b5563;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}
</style>
