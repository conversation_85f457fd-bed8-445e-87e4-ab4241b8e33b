<template>
    <div>
        <button
            v-for="star in 5"
            :key="star"
            type="button"
            @click="selectRating(star)"
        >
            {{ star <= d_value ? '★' : '☆' }}
        </button>
    </div>
</template>

<script setup lang="ts">
import { useFormField } from './useFormField';

interface Props {
    name?: string;
    modelValue?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    'update:modelValue': [value: number];
}>();

// 使用表单字段composable
const { d_value, writeValue } = useFormField(props, emit);

// 事件处理
const selectRating = (rating: number) => {
    writeValue(rating);
};
</script>
