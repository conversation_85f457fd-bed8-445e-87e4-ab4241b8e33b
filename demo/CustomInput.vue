<template>
    <div>
        <label v-if="label">{{ label }}</label>
        <input
            type="text"
            :value="d_value"
            :name="name"
            :disabled="disabled"
            :placeholder="placeholder"
            @input="onInput"
            @blur="onBlur"
            @change="onChange"
            v-bind="formFieldAttrs"
        />
        <small v-if="$invalid && errorMessage">{{ errorMessage }}</small>
    </div>
</template>

<script>
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomInput',
    extends: BaseEditableHolder,
    inheritAttrs: false,
    emits: ['update:modelValue', 'value-change', 'focus', 'blur', 'change'],
    props: {
        label: String,
        placeholder: String
    },
    computed: {
        // 获取表单字段属性，这些属性由Form组件通过register方法提供
        formFieldAttrs() {
            return this.formField || {};
        },
        // 获取错误信息
        errorMessage() {
            return this.formField?.error?.message || this.$pcFormField?.field?.error?.message;
        }
    },
    methods: {
        onInput(event) {
            // 调用父类的writeValue方法，这会触发表单状态更新
            this.writeValue(event.target.value, event);
        },
        onBlur(event) {
            this.$emit('blur', event);
            // 触发表单字段的onBlur事件（用于验证）
            this.formField.onBlur?.(event);
        },
        onChange(event) {
            this.formField.onChange?.(event);
        }
    }
};
</script>


