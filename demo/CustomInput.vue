<template>
    <div class="custom-input-wrapper">
        <label v-if="label" :for="inputId" class="custom-label">{{ label }}</label>
        <div class="custom-input-container" :class="{ 'invalid': $invalid, 'disabled': disabled }">
            <input
                :id="inputId"
                ref="inputRef"
                type="text"
                :value="d_value"
                :name="name"
                :disabled="disabled"
                :placeholder="placeholder"
                :aria-invalid="$invalid || undefined"
                class="custom-input"
                @input="onInput"
                @blur="onBlur"
                @focus="onFocus"
                @change="onChange"
                v-bind="formFieldAttrs"
            />
            <span v-if="icon" :class="['custom-icon', icon]"></span>
        </div>
        <small v-if="$invalid && errorMessage" class="custom-error">{{ errorMessage }}</small>
    </div>
</template>

<script>
import BaseEditableHolder from '@primevue/core/baseeditableholder';

export default {
    name: 'CustomInput',
    extends: BaseEditableHolder,
    inheritAttrs: false,
    emits: ['update:modelValue', 'value-change', 'focus', 'blur', 'change'],
    props: {
        label: {
            type: String,
            default: undefined
        },
        placeholder: {
            type: String,
            default: undefined
        },
        icon: {
            type: String,
            default: undefined
        },
        inputId: {
            type: String,
            default: undefined
        }
    },
    computed: {
        // 获取表单字段属性，这些属性由Form组件通过register方法提供
        formFieldAttrs() {
            return this.formField || {};
        },
        // 获取错误信息
        errorMessage() {
            return this.formField?.error?.message || this.$pcFormField?.field?.error?.message;
        }
    },
    methods: {
        onInput(event) {
            // 调用父类的writeValue方法，这会触发表单状态更新
            this.writeValue(event.target.value, event);
        },
        onBlur(event) {
            this.$emit('blur', event);
            // 触发表单字段的onBlur事件（用于验证）
            this.formField.onBlur?.(event);
        },
        onFocus(event) {
            this.$emit('focus', event);
        },
        onChange(event) {
            this.$emit('change', event);
            // 触发表单字段的onChange事件
            this.formField.onChange?.(event);
        }
    }
};
</script>

<style scoped>
.custom-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.custom-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.custom-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.custom-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: white;
}

.custom-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-input-container.invalid .custom-input {
    border-color: #ef4444;
}

.custom-input-container.invalid .custom-input:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.custom-input-container.disabled .custom-input {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

.custom-icon {
    position: absolute;
    right: 0.75rem;
    color: #6b7280;
    pointer-events: none;
}

.custom-error {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}
</style>
