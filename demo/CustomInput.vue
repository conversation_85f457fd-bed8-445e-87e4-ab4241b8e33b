<template>
    <div>
        <label v-if="label">{{ label }}</label>
        <input
            type="text"
            :value="d_value"
            :name="name"
            :disabled="disabled"
            :placeholder="placeholder"
            @input="onInput"
            @blur="onBlur"
            @change="onChange"
            v-bind="formFieldAttrs"
        />
        <small v-if="$invalid && errorMessage">{{ errorMessage }}</small>
    </div>
</template>

<script setup>
import { useFormField } from './useFormField.js';

// 定义props
const props = defineProps({
    label: String,
    placeholder: String,
    name: String,
    disabled: Boolean,
    modelValue: null,
    defaultValue: null,
    invalid: Boolean,
    formControl: Object
});

// 定义emits
const emit = defineEmits(['update:modelValue', 'value-change', 'focus', 'blur', 'change']);

// 使用表单字段composable
const { d_value, formField, $invalid, errorMessage, writeValue } = useFormField(props, emit);

// 事件处理方法
const onInput = (event) => {
    writeValue(event.target.value, event);
};

const onBlur = (event) => {
    emit('blur', event);
    formField.value.onBlur?.(event);
};

const onChange = (event) => {
    formField.value.onChange?.(event);
};
</script>


