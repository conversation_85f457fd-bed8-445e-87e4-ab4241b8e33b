<template>
    <DocSectionText v-bind="$attrs">
        <p>
            The <NuxtLink to="/theming/styled">theming api</NuxtLink> is open and source freely available with an extensive documentation. Theme Designer is a tool build on top of this theming api with important features to make theming easier.
            Designer consists of 4 key features; The <b>visual editor</b> provides a UI to edit the complete set of tokens. The <b>figma to code</b> generator is extremely useful to automate the design to code process and integrates seamlessly with
            the Figma UI Kit. The themes are saved in the <b>cloud storage </b>to be accessible from anywhere and any device and finally the <b>migration assistant</b> automatically updates your themes to the latest library version.
        </p>
    </DocSectionText>
</template>
