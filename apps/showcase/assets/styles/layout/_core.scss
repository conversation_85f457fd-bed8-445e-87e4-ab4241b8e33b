html {
    font-size: 14px;
    font-family: "Inter var", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    line-height: normal;
}

body {
    margin: 0px;
    min-height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--ground-background);
    font-weight: normal;
    color: var(--text-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.layout-wrapper {
    background-color: var(--ground-background);
}

a {
    text-decoration: none;
}

::selection {
    background-color: var(--selection-background);
    color: var(--selection-text-color);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-block: 1.5rem 1rem;
    margin-inline: 0;
    font-family: inherit;
    font-weight: 600;
    line-height: 1.2;
    color: var(--high-contrast-text-color);

    &:first-child {
        margin-top: 0;
    }
}

h1 {
    font-size: 1.75rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.25rem;
}

h4 {
    font-size: 1.125rem;
}

h5 {
    font-size: 1rem;
}

h6 {
    font-size: .875rem;
}

p {
    line-height: 1.625;
    margin-block: 0 1rem;
    margin-inline: 0;
}

.p-toast.p-toast-top-right,
.p-toast.p-toast-top-left {
    top: 7rem;
}

button {
    font-family: inherit;
    font-feature-settings: inherit;
}

textarea {
    resize: none;
}