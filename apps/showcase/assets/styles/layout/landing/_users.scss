.landing-users {
    .fade-right {
        background: linear-gradient(to left, var(--ground-background), transparent);
    }
    .fade-left {
        background: linear-gradient(to right, var(--ground-background), transparent);
    }

    .section-detail {
        max-width: 1250px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .users-container {
        max-width: 1250px !important;
        margin-left: auto !important;
        margin-right: auto !important;

        .marquee-wrapper {
            user-select: none;
            gap: 3rem;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }
        .marquee {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: space-around;
            gap: 3rem;
            min-width: 100%;
            animation: scroll 30s linear infinite;

            > div {
                max-width: clamp(10rem, 1rem + 28vmin, 20rem);
                aspect-ratio: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 8rem;
            }

            &.marquee-reverse{
                animation-direction: reverse;
                animation-delay: calc(30s / -2);
            }
        }
    }
}