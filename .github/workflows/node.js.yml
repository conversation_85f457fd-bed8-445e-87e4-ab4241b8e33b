name: NodeJS CI

on:
    push:
        branches: [master]

permissions:
    contents: write

jobs:
    build:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [18, 20]

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: 9.6.0

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ matrix.node-version }}
                  cache: 'pnpm'

            - name: Build
              run: |
                  pnpm run setup

            - name: Lint Check
              if: ${{ success() }}
              run: |
                  pnpm run lint

            - name: Code Format Check
              id: codeFormatCheck
              if: ${{ success() }}
              run: |
                  pnpm run format:check

            - name: Code Format
              if: always() && steps.codeFormatCheck.outcome == 'failure'
              run: |
                  npm run format
                  git config user.name "GitHub Actions Bot"
                  git config user.email "<>"
                  git commit -a -m "Code Format"
                  git push

            - name: Security Check
              if: ${{ success() }}
              run: |
                  pnpm run security:check

            # - name: Unit Tests Check
            #   if: ${{ success() }}
            #   run: |
            #       npm run test:unit
