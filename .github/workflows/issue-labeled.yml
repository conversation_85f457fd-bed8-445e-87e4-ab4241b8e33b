name: Issue Labeled

on:
  issues:
    types: [labeled]

permissions:
  contents: read

jobs:
  issue-labeled:
    permissions:
      issues: write  # for actions-cool/issues-helper to update issues
      pull-requests: write  # for actions-cool/issues-helper to update PRs
    runs-on: ubuntu-latest
    steps:
      - name: Cannot Replicate
        if: "${{ github.event.label.name == 'Resolution: Cannot Replicate'}}"
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            We're unable to replicate your issue, if you are able to create a reproducer by using [PrimeVue Issue Template](https://stackblitz.com/edit/primevue-4-vite-issue-template) or add details please edit this issue. This issue will be closed if no activities in 20 days.

      - name: Needs Upvote
        if: contains(github.event.label.name, 'Needs Upvote')
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Thanks a lot for this issue! PrimeTek's own vision for PrimeVue is demanding, but community feedback is crucial in prioritization. The more upvotes help ensure this fix can be addressed quickly or the related PR can be merged soon.

      - name: Needs More Information
        if: contains(github.event.label.name, 'Needs More Information')
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            More information is needed to find a solution. A runnable StackBlitz example and additional details would be helpful.

      - name: Wontfix
        if: contains(github.event.label.name, 'Wontfix')
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Unfortunately, this issue cannot be fixed due to technical limitations. PrimeVue team has decided not to pursue a fix, as addressing it would introduce other complications. Thanks a lot for your understanding!

      - name: Help Wanted
        if: contains(github.event.label.name, 'Help Wanted')
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Due to PrimeTek's demanding roadmap for PrimeVue and the limited bandwidth of the core team, this issue is available for anyone to work on. [**Make sure to reference this issue in your pull request.**](https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue) :sparkles: Thank you for your contribution! :sparkles:

      - name: Duplicate
        if: contains(github.event.label.name, 'Duplicate')
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment, close-issue'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            This issue has been marked as a duplicate and has been closed. Please refer to the original issue for updates. Thank you!

